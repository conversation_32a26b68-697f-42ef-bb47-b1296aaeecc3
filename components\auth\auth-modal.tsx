"use client";

import React, { useState, useEffect } from "react";
import { signIn } from "next-auth/react";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LogIn, UserPlus, Shield, ArrowRight, Eye, EyeOff, User, Mail, Lock, Phone, Check } from "lucide-react";
import { LoadingSpinner } from "../ui/loading-spinner";
import { cn } from "@/lib/utils";

interface AuthModalProps {
  open: boolean;
  onClose: () => void;
  message?: string;
  returnUrl?: string;
  showSignUp?: boolean;
  title?: string;
  description?: string;
  defaultTab?: 'login' | 'signup';
}

/**
 * AuthModal - A beautiful, consistent authentication modal with inline forms
 * Provides unified UX for all authentication scenarios without external redirects
 */
export function AuthModal({
  open,
  onClose,
  message = "Please sign in to continue",
  returnUrl,
  showSignUp = true,
  title = "Welcome",
  description = "Sign in to your account or create a new one to continue",
  defaultTab = 'login'
}: AuthModalProps) {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>(defaultTab);
  const [isLoading, setIsLoading] = useState(false);

  // Login form state
  const [loginForm, setLoginForm] = useState({
    username: '',
    password: '',
    showPassword: false
  });

  // Signup form state with all required fields
  const [signupForm, setSignupForm] = useState({
    // Step 1: Basic Info
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
    // Step 2: Account Details
    username: '',
    password: '',
    confirmPassword: '',
    showPassword: false,
    showConfirmPassword: false
  });

  // Stepper state for signup
  const [signupStep, setSignupStep] = useState(1);
  const totalSignupSteps = 2;

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when modal opens/closes
  useEffect(() => {
    if (open) {
      setActiveTab(defaultTab);
      setErrors({});
      setSignupStep(1);
      setLoginForm({ username: '', password: '', showPassword: false });
      setSignupForm({
        firstName: '', lastName: '', email: '', mobile: '',
        username: '', password: '', confirmPassword: '',
        showPassword: false, showConfirmPassword: false
      });
    }
  }, [open, defaultTab]);

  // Validation functions
  const validateLoginForm = () => {
    const newErrors: Record<string, string> = {};

    if (!loginForm.username.trim()) {
      newErrors.username = 'Username is required';
    }

    if (!loginForm.password) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateSignupStep = (step: number) => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      // Step 1: Basic Info validation
      if (!signupForm.firstName.trim()) {
        newErrors.firstName = 'First name is required';
      }

      if (!signupForm.lastName.trim()) {
        newErrors.lastName = 'Last name is required';
      }

      if (!signupForm.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(signupForm.email)) {
        newErrors.email = 'Please enter a valid email address';
      }

      if (!signupForm.mobile.trim()) {
        newErrors.mobile = 'Mobile number is required';
      } else if (!/^\d{10}$/.test(signupForm.mobile.replace(/\D/g, ''))) {
        newErrors.mobile = 'Please enter a valid 10-digit mobile number';
      }
    } else if (step === 2) {
      // Step 2: Account Details validation
      if (!signupForm.username.trim()) {
        newErrors.username = 'Username is required';
      } else if (signupForm.username.length < 3) {
        newErrors.username = 'Username must be at least 3 characters';
      }

      if (!signupForm.password) {
        newErrors.password = 'Password is required';
      } else if (signupForm.password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }

      if (signupForm.password !== signupForm.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateSignupForm = () => {
    return validateSignupStep(1) && validateSignupStep(2);
  };

  // Handle login form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateLoginForm()) return;

    try {
      setIsLoading(true);
      setErrors({});

      // Store return URL if provided
      if (returnUrl) {
        localStorage.setItem("auth_return_url", returnUrl);
      }

      // Format username (add country code if it's a phone number)
      let formattedUsername = loginForm.username.trim();
      if (/^\d{10}$/.test(formattedUsername)) {
        formattedUsername = `91${formattedUsername}`;
      }

      // Use credentials provider for inline login
      const result = await signIn('credentials', {
        username: formattedUsername,
        password: loginForm.password,
        redirect: false,
      });

      if (result?.error) {
        setErrors({ general: 'Invalid credentials. Please try again.' });
        setIsLoading(false);
      } else {
        // Success - close modal and redirect if needed
        toast.success("Successfully signed in!", {
          icon: <Shield className="h-4 w-4" />
        });
        onClose();

        // Redirect to return URL if provided
        if (returnUrl && returnUrl !== window.location.href) {
          window.location.href = returnUrl;
        } else {
          // Refresh the page to update auth state
          window.location.reload();
        }
      }

    } catch (error) {
      console.error("Login error:", error);
      setErrors({ general: 'An error occurred. Please try again.' });
      setIsLoading(false);
    }
  };

  // Handle signup form submission
  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateSignupForm()) return;

    try {
      setIsLoading(true);
      setErrors({});

      // For now, redirect to Keycloak registration
      // In the future, this could be replaced with a custom registration API
      toast.info("Redirecting to create your account...", {
        icon: <UserPlus className="h-4 w-4" />
      });

      // Store return URL and form data
      if (returnUrl) {
        localStorage.setItem("auth_return_url", returnUrl);
      }

      // Redirect to Keycloak registration
      await signIn("keycloak", {
        callbackUrl: returnUrl || window.location.href,
        redirect: true
      });

    } catch (error) {
      console.error("Signup error:", error);
      setErrors({ general: 'An error occurred. Please try again.' });
      setIsLoading(false);
    }
  };

  // Handle stepper navigation
  const handleStepNext = () => {
    if (validateSignupStep(signupStep)) {
      if (signupStep < totalSignupSteps) {
        setSignupStep(signupStep + 1);
        setErrors({}); // Clear errors when moving to next step
      }
    }
  };

  const handleStepBack = () => {
    if (signupStep > 1) {
      setSignupStep(signupStep - 1);
      setErrors({}); // Clear errors when going back
    }
  };

  const handleClose = () => {
    if (isLoading) return;
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <Shield className="h-6 w-6 text-primary" />
          </div>
          <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {description}
          </DialogDescription>
        </DialogHeader>

        {message && (
          <div className="rounded-lg bg-muted/50 p-3 text-center text-sm text-muted-foreground mb-4">
            {message}
          </div>
        )}

        {errors.general && (
          <div className="rounded-lg bg-destructive/10 border border-destructive/20 p-3 text-center text-sm text-destructive mb-4">
            {errors.general}
          </div>
        )}

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'login' | 'signup')} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="login" className="flex items-center gap-2">
              <LogIn className="h-4 w-4" />
              Sign In
            </TabsTrigger>
            {showSignUp && (
              <TabsTrigger value="signup" className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                Sign Up
              </TabsTrigger>
            )}
          </TabsList>

          {/* Login Tab */}
          <TabsContent value="login" className="space-y-4 mt-6">
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="login-username" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Username or Phone
                </Label>
                <Input
                  id="login-username"
                  type="text"
                  placeholder="Enter your username or phone number"
                  value={loginForm.username}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, username: e.target.value }))}
                  className={cn(errors.username && "border-destructive")}
                  disabled={isLoading}
                />
                {errors.username && (
                  <p className="text-sm text-destructive">{errors.username}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="login-password" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="login-password"
                    type={loginForm.showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={loginForm.password}
                    onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                    className={cn(errors.password && "border-destructive", "pr-10")}
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setLoginForm(prev => ({ ...prev, showPassword: !prev.showPassword }))}
                    disabled={isLoading}
                  >
                    {loginForm.showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-destructive">{errors.password}</p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full"
                size="lg"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Signing in...
                  </>
                ) : (
                  <>
                    Sign In
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </form>
          </TabsContent>

          {/* Signup Tab */}
          {showSignUp && (
            <TabsContent value="signup" className="space-y-4 mt-6">
              {/* Custom Stepper Header */}
              <div className="flex items-center justify-between mb-6">
                {[
                  { label: "Personal Info", step: 1 },
                  { label: "Account Setup", step: 2 }
                ].map((item, index) => (
                  <React.Fragment key={item.step}>
                    <div className="flex flex-col items-center">
                      <div
                        className={cn(
                          "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-200",
                          {
                            "bg-primary border-primary text-primary-foreground": signupStep >= item.step,
                            "bg-background border-muted-foreground text-muted-foreground": signupStep < item.step,
                          }
                        )}
                      >
                        {signupStep > item.step ? (
                          <Check className="w-4 h-4" />
                        ) : (
                          <span className="text-sm font-medium">{item.step}</span>
                        )}
                      </div>
                      <span
                        className={cn(
                          "mt-2 text-xs font-medium text-center",
                          {
                            "text-primary": signupStep >= item.step,
                            "text-muted-foreground": signupStep < item.step,
                          }
                        )}
                      >
                        {item.label}
                      </span>
                    </div>
                    {index < 1 && (
                      <div className="flex-1 mx-4">
                        <div
                          className={cn(
                            "h-0.5 transition-all duration-200",
                            {
                              "bg-primary": signupStep > item.step,
                              "bg-muted": signupStep <= item.step,
                            }
                          )}
                        />
                      </div>
                    )}
                  </React.Fragment>
                ))}
              </div>

              <form onSubmit={handleSignup} className="space-y-4">
                    {/* Step 1: Personal Information */}
                    {signupStep === 1 && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="signup-firstName" className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              First Name *
                            </Label>
                            <Input
                              id="signup-firstName"
                              type="text"
                              placeholder="Enter first name"
                              value={signupForm.firstName}
                              onChange={(e) => setSignupForm(prev => ({ ...prev, firstName: e.target.value }))}
                              className={cn(errors.firstName && "border-destructive")}
                              disabled={isLoading}
                            />
                            {errors.firstName && (
                              <p className="text-sm text-destructive">{errors.firstName}</p>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="signup-lastName" className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              Last Name *
                            </Label>
                            <Input
                              id="signup-lastName"
                              type="text"
                              placeholder="Enter last name"
                              value={signupForm.lastName}
                              onChange={(e) => setSignupForm(prev => ({ ...prev, lastName: e.target.value }))}
                              className={cn(errors.lastName && "border-destructive")}
                              disabled={isLoading}
                            />
                            {errors.lastName && (
                              <p className="text-sm text-destructive">{errors.lastName}</p>
                            )}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="signup-email" className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            Email Address *
                          </Label>
                          <Input
                            id="signup-email"
                            type="email"
                            placeholder="Enter your email address"
                            value={signupForm.email}
                            onChange={(e) => setSignupForm(prev => ({ ...prev, email: e.target.value }))}
                            className={cn(errors.email && "border-destructive")}
                            disabled={isLoading}
                          />
                          {errors.email && (
                            <p className="text-sm text-destructive">{errors.email}</p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="signup-mobile" className="flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            Mobile Number *
                          </Label>
                          <Input
                            id="signup-mobile"
                            type="tel"
                            placeholder="Enter your mobile number"
                            value={signupForm.mobile}
                            onChange={(e) => setSignupForm(prev => ({ ...prev, mobile: e.target.value }))}
                            className={cn(errors.mobile && "border-destructive")}
                            disabled={isLoading}
                          />
                          {errors.mobile && (
                            <p className="text-sm text-destructive">{errors.mobile}</p>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Step 2: Account Details */}
                    {signupStep === 2 && (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="signup-username" className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            Username *
                          </Label>
                          <Input
                            id="signup-username"
                            type="text"
                            placeholder="Choose a username"
                            value={signupForm.username}
                            onChange={(e) => setSignupForm(prev => ({ ...prev, username: e.target.value }))}
                            className={cn(errors.username && "border-destructive")}
                            disabled={isLoading}
                          />
                          {errors.username && (
                            <p className="text-sm text-destructive">{errors.username}</p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="signup-password" className="flex items-center gap-2">
                            <Lock className="h-4 w-4" />
                            Password *
                          </Label>
                          <div className="relative">
                            <Input
                              id="signup-password"
                              type={signupForm.showPassword ? "text" : "password"}
                              placeholder="Create a password"
                              value={signupForm.password}
                              onChange={(e) => setSignupForm(prev => ({ ...prev, password: e.target.value }))}
                              className={cn(errors.password && "border-destructive", "pr-10")}
                              disabled={isLoading}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={() => setSignupForm(prev => ({ ...prev, showPassword: !prev.showPassword }))}
                              disabled={isLoading}
                            >
                              {signupForm.showPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                          {errors.password && (
                            <p className="text-sm text-destructive">{errors.password}</p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="signup-confirm-password" className="flex items-center gap-2">
                            <Lock className="h-4 w-4" />
                            Confirm Password *
                          </Label>
                          <div className="relative">
                            <Input
                              id="signup-confirm-password"
                              type={signupForm.showConfirmPassword ? "text" : "password"}
                              placeholder="Confirm your password"
                              value={signupForm.confirmPassword}
                              onChange={(e) => setSignupForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                              className={cn(errors.confirmPassword && "border-destructive", "pr-10")}
                              disabled={isLoading}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={() => setSignupForm(prev => ({ ...prev, showConfirmPassword: !prev.showConfirmPassword }))}
                              disabled={isLoading}
                            >
                              {signupForm.showConfirmPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                          {errors.confirmPassword && (
                            <p className="text-sm text-destructive">{errors.confirmPassword}</p>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Custom Navigation */}
                    <div className="flex justify-between mt-6 pt-4 border-t">
                      {signupStep > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleStepBack}
                          disabled={isLoading}
                        >
                          Previous
                        </Button>
                      )}

                      <div className={signupStep === 1 ? "ml-auto" : ""}>
                        {signupStep < totalSignupSteps ? (
                          <Button
                            type="button"
                            onClick={handleStepNext}
                            disabled={isLoading}
                          >
                            Next
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        ) : (
                          <Button
                            type="submit"
                            disabled={isLoading}
                          >
                            {isLoading ? (
                              <>
                                <LoadingSpinner size="sm" className="mr-2" />
                                Creating account...
                              </>
                            ) : (
                              <>
                                Create Account
                                <ArrowRight className="ml-2 h-4 w-4" />
                              </>
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  </form>
            </TabsContent>
          )}
        </Tabs>

        <div className="text-center text-xs text-muted-foreground mt-6">
          By continuing, you agree to our Terms of Service and Privacy Policy.
        </div>
      </DialogContent>
    </Dialog>
  );
}
