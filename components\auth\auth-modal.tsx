"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { LogIn, UserPlus, Shield, ArrowRight } from "lucide-react";
import { LoadingSpinner } from "../ui/loading-spinner";

interface AuthModalProps {
  open: boolean;
  onClose: () => void;
  message?: string;
  returnUrl?: string;
  showSignUp?: boolean;
  title?: string;
  description?: string;
}

/**
 * AuthModal - A beautiful, consistent authentication modal
 * Provides unified UX for all authentication scenarios
 */
export function AuthModal({
  open,
  onClose,
  message = "Please sign in to continue",
  returnUrl,
  showSignUp = true,
  title = "Authentication Required",
  description = "Sign in to access this feature and enjoy a personalized experience."
}: AuthModalProps) {
  const [isSigningIn, setIsSigningIn] = useState(false);
  const [isRedirectingToSignUp, setIsRedirectingToSignUp] = useState(false);

  const handleSignIn = async () => {
    try {
      setIsSigningIn(true);
      
      // Store return URL if provided
      if (returnUrl) {
        localStorage.setItem("auth_return_url", returnUrl);
      }

      // Use Keycloak for consistent authentication
      await signIn("keycloak", { 
        callbackUrl: returnUrl || window.location.href,
        redirect: true 
      });
      
      // Show loading toast
      toast.info("Redirecting to sign in...", {
        duration: 3000,
        icon: <Shield className="h-4 w-4" />
      });
      
    } catch (error) {
      console.error("Sign in error:", error);
      toast.error("Failed to initiate sign in. Please try again.");
      setIsSigningIn(false);
    }
  };

  const handleSignUp = () => {
    try {
      setIsRedirectingToSignUp(true);
      
      // Store return URL for after signup
      if (returnUrl) {
        localStorage.setItem("auth_return_url", returnUrl);
      }

      // Navigate to signup page
      const signupUrl = `/signup${returnUrl ? `?returnUrl=${encodeURIComponent(returnUrl)}` : ''}`;
      window.location.href = signupUrl;
      
      toast.info("Redirecting to sign up...", {
        duration: 3000,
        icon: <UserPlus className="h-4 w-4" />
      });
      
    } catch (error) {
      console.error("Sign up redirect error:", error);
      toast.error("Failed to redirect to sign up. Please try again.");
      setIsRedirectingToSignUp(false);
    }
  };

  const handleClose = () => {
    if (isSigningIn || isRedirectingToSignUp) return;
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <Shield className="h-6 w-6 text-primary" />
          </div>
          <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {message && (
            <div className="rounded-lg bg-muted/50 p-3 text-center text-sm text-muted-foreground">
              {message}
            </div>
          )}

          <Button
            onClick={handleSignIn}
            disabled={isSigningIn || isRedirectingToSignUp}
            className="w-full"
            size="lg"
          >
            {isSigningIn ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Signing in...
              </>
            ) : (
              <>
                <LogIn className="mr-2 h-4 w-4" />
                Sign In
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>

          {showSignUp && (
            <>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <Separator className="w-full" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    Or
                  </span>
                </div>
              </div>

              <Button
                onClick={handleSignUp}
                disabled={isSigningIn || isRedirectingToSignUp}
                variant="outline"
                className="w-full"
                size="lg"
              >
                {isRedirectingToSignUp ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Redirecting...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Create Account
                  </>
                )}
              </Button>
            </>
          )}
        </div>

        <div className="text-center text-xs text-muted-foreground">
          By continuing, you agree to our Terms of Service and Privacy Policy.
        </div>
      </DialogContent>
    </Dialog>
  );
}
