'use client';

import React, { useEffect, useState, useCallback, useRef } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { CheckCircle2, Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { useUser } from '@/context/user-context';
import { createSubscription, SubscriptionResponse, CartItem } from '@/src/services/lago/subscriptionService';
import { getLatestInvoiceByCustomerId, updateInvoicePaymentStatus } from '@/src/services/lago/invoiceService';
import { toast } from 'sonner';
import { AutoPaymentStatusUpdater } from '@/components/payment/auto-payment-status-updater';

interface PaymentDetailsState {
  timestamp: string;
  items?: Array<CartItem>;
  status: 'idle' | 'processing' | 'success' | 'error' | 'timeout';
  paymentId?: string;
  error?: string;
  retryCount?: number;
}

// Type for the stored cart items that might be missing some required fields
interface StoredCartItem {
  id?: string;
  name?: string;
  planCode?: string;
  [key: string]: unknown;
}

// Constants for timeout and retry logic
const PROCESSING_TIMEOUT = 60000; // 60 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 5000; // 5 seconds

export default function PaymentSuccessPage() {
  const { userId, isLoading } = useUser();
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetailsState>({
    timestamp: '',
    items: [],
    status: 'idle',
    retryCount: 0
  });
  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasInitialized = useRef(false);
  const isProcessingRef = useRef(false);

  // Helper for logging
  const logPaymentProcess = useCallback((step: string, data?: unknown, status: 'info' | 'success' | 'warning' | 'error' = 'info') => {
    console.log(`[Payment Success] ${step}`, data, status);
    console.log(`[Payment Success] ${step}`, data);
  }, []);

  // Clear any existing timeout
  const clearProcessingTimeout = useCallback(() => {
    if (processingTimeoutRef.current) {
      clearTimeout(processingTimeoutRef.current);
      processingTimeoutRef.current = null;
    }
  }, []);

  // Set processing timeout
  const setProcessingTimeout = useCallback(() => {
    clearProcessingTimeout();
    processingTimeoutRef.current = setTimeout(() => {
      logPaymentProcess('Processing timeout reached');
      setPaymentDetails(prev => ({
        ...prev,
        status: 'timeout',
        error: 'Processing took too long. Please refresh the page or contact support.'
      }));
      isProcessingRef.current = false;
    }, PROCESSING_TIMEOUT);
  }, [clearProcessingTimeout, logPaymentProcess]);

  // Retry function
  const retryPaymentProcessing = useCallback(() => {
    const currentRetryCount = paymentDetails.retryCount || 0;
    if (currentRetryCount >= MAX_RETRIES) {
      setPaymentDetails(prev => ({
        ...prev,
        status: 'error',
        error: 'Maximum retry attempts reached. Please contact support.'
      }));
      return;
    }

    logPaymentProcess(`Retrying payment processing (attempt ${currentRetryCount + 1}/${MAX_RETRIES})`);
    setPaymentDetails(prev => ({
      ...prev,
      status: 'processing',
      retryCount: currentRetryCount + 1,
      error: undefined
    }));

    setTimeout(() => {
      handleSuccessfulPayment();
    }, RETRY_DELAY);
  }, [paymentDetails.retryCount, logPaymentProcess]);

  const handleSuccessfulPayment = useCallback(async () => {
    // Prevent concurrent processing
    if (isProcessingRef.current) {
      logPaymentProcess('Payment processing already in progress, skipping');
      return;
    }

    if (paymentDetails.status === 'processing' || paymentDetails.status === 'success') {
      return;
    }

    isProcessingRef.current = true;
    setPaymentDetails(prev => ({ ...prev, status: 'processing', error: undefined }));
    setProcessingTimeout();
    logPaymentProcess('Starting subscription and invoice flow');

    // Validate user ID (Keycloak ID)
    if (!userId) {
      logPaymentProcess('No user ID found, aborting', { isLoading, hasInitialized: hasInitialized.current }, 'error');
      setPaymentDetails(prev => ({ ...prev, status: 'error', error: 'User identification failed' }));
      toast.error('User identification failed. Please contact support.');
      clearProcessingTimeout();
      isProcessingRef.current = false;
      return;
    }

    try {
      // Step 1: Create subscription in Lago
      logPaymentProcess('Step 1: Creating subscription in Lago', { userId, items: paymentDetails.items });

      let latestSubscriptionResult: SubscriptionResponse | null = null;

      // Process each item in the cart
      if (paymentDetails.items && paymentDetails.items.length > 0) {
        for (const item of paymentDetails.items) {
          if (!item.planCode) {
            logPaymentProcess(`Item ${item.name} has no plan code, skipping`);
            continue;
          }

          logPaymentProcess(`Creating subscription for ${item.name}`, { planCode: item.planCode });

          const subscriptionResult = await createSubscription(userId, item);

          if (subscriptionResult?.subscription) {
            logPaymentProcess('Subscription created successfully', {
              id: subscriptionResult.subscription.id,
              planCode: subscriptionResult.subscription.plan_code
            });
            latestSubscriptionResult = subscriptionResult;
          } else {
            logPaymentProcess(`Failed to create subscription for ${item.name}`, { error: subscriptionResult?.error });
          }
        }
      } else {
        logPaymentProcess('No items found in cart, skipping subscription creation');
        setPaymentDetails(prev => ({ ...prev, status: 'error' }));
        toast.error('No subscription items found. Please contact support.');
        return;
      }

      if (!latestSubscriptionResult?.subscription) {
        logPaymentProcess('No subscriptions were created successfully');
        setPaymentDetails(prev => ({ ...prev, status: 'error' }));
        toast.error('Failed to create subscription. Please contact support.');
        return;
      }

      // Step 2: Wait briefly for invoice generation
      logPaymentProcess('Step 2: Waiting for invoice generation');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Step 3: Fetch the invoice
      const subscriptionDate = latestSubscriptionResult.subscription.created_at;
      const formattedDate = new Date(subscriptionDate).toISOString().split('T')[0];

      logPaymentProcess('Step 3: Fetching invoice details', {
        external_customer_id: userId,
        issuing_date_from: formattedDate
      });

      const invoice = await getLatestInvoiceByCustomerId(userId, formattedDate);

      if (!invoice) {
        logPaymentProcess('No invoice found, waiting and retrying');
        // Try one more time after a delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        const retryInvoice = await getLatestInvoiceByCustomerId(userId, formattedDate);

        if (!retryInvoice) {
          logPaymentProcess('Invoice not found after retry');
          setPaymentDetails(prev => ({ ...prev, status: 'error' }));
          toast.error('Invoice not found. Subscription created but payment status not updated.');
          return;
        }

        logPaymentProcess('Invoice found after retry', { lagoId: retryInvoice.lago_id });
      }

      const invoiceToUpdate = invoice || await getLatestInvoiceByCustomerId(userId, formattedDate);

      if (!invoiceToUpdate) {
        logPaymentProcess('Could not find any invoice to update');
        setPaymentDetails(prev => ({ ...prev, status: 'error' }));
        return;
      }

      // Step 4: Update the payment status
      logPaymentProcess('Step 4: Updating payment status', {
        lagoId: invoiceToUpdate.lago_id,
        current_status: invoiceToUpdate.payment_status
      });

      const updateResult = await updateInvoicePaymentStatus(invoiceToUpdate.lago_id);

      if (updateResult) {
        logPaymentProcess('Payment status updated successfully to "succeeded"', { invoiceId: invoiceToUpdate.lago_id }, 'success');
        clearProcessingTimeout();
        setPaymentDetails(prev => ({ ...prev, status: 'success' }));
        toast.success('Payment completed successfully!');
      } else {
        logPaymentProcess('Failed to update payment status', { invoiceId: invoiceToUpdate.lago_id }, 'error');
        setPaymentDetails(prev => ({
          ...prev,
          status: 'error',
          error: 'Failed to update payment status'
        }));
        toast.error('Failed to update payment status. Please contact support.');
      }

    } catch (error) {
      logPaymentProcess('Error in payment flow', error, 'error');
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setPaymentDetails(prev => ({
        ...prev,
        status: 'error',
        error: errorMessage
      }));
      toast.error('An error occurred during payment processing. Please contact support.');
    } finally {
      clearProcessingTimeout();
      isProcessingRef.current = false;
    }
  }, [paymentDetails.status, paymentDetails.items, userId, logPaymentProcess, setProcessingTimeout, clearProcessingTimeout]);

  // Initialize payment details from localStorage
  useEffect(() => {
    if (hasInitialized.current) {
      return;
    }

    try {
      logPaymentProcess('Initializing payment details from localStorage');

      const timestamp = localStorage.getItem('payment_success_timestamp');
      const itemsJson = localStorage.getItem('payment_cart_items');
      const paymentStatus = localStorage.getItem('payment_status');
      const paymentId = localStorage.getItem('last_payment_id');

      // Log all the values for debugging
      logPaymentProcess('Payment details from localStorage', {
        timestamp: timestamp ? 'present' : 'missing',
        itemsJson: itemsJson ? 'present' : 'missing',
        paymentStatus,
        paymentId,
        userId: userId || 'missing',
        isLoading
      });

      if (timestamp) {
        const parsedItems: StoredCartItem[] = itemsJson ? JSON.parse(itemsJson) : [];

        // Ensure each item has an id field
        const items = parsedItems.map((item: StoredCartItem) => ({
          id: item.id || `item-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          name: item.name || 'Subscription',
          planCode: item.planCode,
          ...item
        })) as CartItem[];

        logPaymentProcess(`Found ${items.length} items in cart`);

        setPaymentDetails({
          timestamp: new Date(parseInt(timestamp)).toLocaleString(),
          items,
          status: 'idle',
          paymentId: paymentId || undefined,
          retryCount: 0
        });

        hasInitialized.current = true;
      } else {
        logPaymentProcess('No payment timestamp found in localStorage');
        setPaymentDetails(prev => ({
          ...prev,
          status: 'error',
          error: 'No payment information found. Please try again or contact support.'
        }));
      }
    } catch (error) {
      console.error('❌ PAYMENT SUCCESS - Error reading payment details:', error);
      setPaymentDetails(prev => ({
        ...prev,
        status: 'error',
        error: 'Failed to load payment information'
      }));
    }
  }, [logPaymentProcess]);

  // Process payment when conditions are met
  useEffect(() => {
    if (!hasInitialized.current || isProcessingRef.current) {
      return;
    }

    const paymentStatus = localStorage.getItem('payment_status');

    if (paymentStatus === 'succeeded' && userId && !isLoading && paymentDetails.status === 'idle') {
      logPaymentProcess('Payment success detected, initiating subscription flow');
      handleSuccessfulPayment();
    }
  }, [userId, isLoading, paymentDetails.status, handleSuccessfulPayment, logPaymentProcess]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      clearProcessingTimeout();
    };
  }, [clearProcessingTimeout]);

  // Get the date from the subscription creation time for pending invoice check
  const getDateFromForInvoices = () => {
    if (paymentDetails.timestamp) {
      try {
        // Try to parse the timestamp from the displayed format
        const date = new Date(paymentDetails.timestamp);
        if (!isNaN(date.getTime())) {
          // If valid date, return in YYYY-MM-DD format
          return date.toISOString().split('T')[0];
        }
      } catch (e) {
        console.error('Error parsing timestamp:', e);
      }
    }
    // Fallback to current date
    return new Date().toISOString().split('T')[0];
  };

  // Render status icon
  const renderStatusIcon = () => {
    switch (paymentDetails.status) {
      case 'processing':
        return <Loader2 size={64} className="text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle2 size={64} className="text-green-500" />;
      case 'error':
      case 'timeout':
        return <AlertCircle size={64} className="text-red-500" />;
      default:
        return <CheckCircle2 size={64} className="text-gray-400" />;
    }
  };

  // Render status message
  const renderStatusMessage = () => {
    switch (paymentDetails.status) {
      case 'success':
        return 'Your subscription has been activated successfully!';
      case 'processing':
        return 'We are activating your subscription... This may take a few moments.';
      case 'timeout':
        return 'Processing is taking longer than expected. You can try refreshing the page or contact support.';
      case 'error':
        return paymentDetails.error || 'There was an issue activating your subscription. Please contact support.';
      default:
        return 'Your subscription is being activated.';
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-4">
      <div className="max-w-3xl w-full mx-auto">
        <div className="bg-white p-8 rounded-lg shadow-lg mb-6 text-center">
          <div className="flex justify-center mb-6">
            {renderStatusIcon()}
          </div>

          <h1 className="text-2xl font-bold mb-2">
            {paymentDetails.status === 'success' ? 'Payment Successful!' :
             paymentDetails.status === 'error' || paymentDetails.status === 'timeout' ? 'Processing Issue' :
             'Payment Received!'}
          </h1>
          <p className="text-gray-600 mb-6">
            Thank you for your payment. {renderStatusMessage()}
          </p>

          {/* Show retry button for errors or timeouts */}
          {(paymentDetails.status === 'error' || paymentDetails.status === 'timeout') &&
           (paymentDetails.retryCount || 0) < MAX_RETRIES && (
            <div className="mb-6">
              <Button
                onClick={retryPaymentProcessing}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw size={16} />
                Retry Processing ({(paymentDetails.retryCount || 0) + 1}/{MAX_RETRIES})
              </Button>
            </div>
          )}

          {/* Show processing progress */}
          {paymentDetails.status === 'processing' && (
            <div className="mb-6">
              <div className="text-sm text-gray-500">
                Processing step {paymentDetails.retryCount ? `(Retry ${paymentDetails.retryCount})` : ''}
              </div>
              <div className="mt-2 text-xs text-gray-400">
                This usually takes 10-30 seconds
              </div>
            </div>
          )}

          {paymentDetails.timestamp && (
            <div className="mb-6 text-sm text-gray-600">
              <p className="mb-2">Payment processed: {paymentDetails.timestamp}</p>

              {paymentDetails.items && paymentDetails.items.length > 0 && (
                <div className="mt-4">
                  <p className="font-semibold mb-1">Subscription details:</p>
                  <ul className="text-left pl-4 list-disc inline-block">
                    {paymentDetails.items.map((item, index) => (
                      <li key={index}>{item.name || item.planCode}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="flex-1">
              <Link href={`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`}>
                Go to Dashboard
              </Link>
            </Button>

            <Button variant="outline" asChild className="flex-1">
              <Link href="/support">
                Need help?
              </Link>
            </Button>
          </div>
        </div>



        {/* Auto Payment Status Updater - only enabled as fallback when main processing fails */}
        {userId && paymentDetails.status === 'success' && (
          <AutoPaymentStatusUpdater
            dateFrom={getDateFromForInvoices()}
            enabled={true}
            paymentId={paymentDetails.paymentId}
            sendEmail={false} // Prevent duplicate emails since main flow handles this
          />
        )}
      </div>
    </div>
  );
}