import { NextRequest, NextResponse } from 'next/server';

interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  username: string;
  password: string;
}

interface KeycloakUser {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  enabled: boolean;
  emailVerified: boolean;
  attributes?: {
    mobile?: string[];
  };
}

/**
 * Production-level user registration with Keycloak
 * Handles all error scenarios including duplicate users
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[REGISTER] POST request received');
    const body: RegisterRequest = await request.json();
    console.log('[REGISTER] Request body:', { ...body, password: '[REDACTED]' });

    // Validate required fields
    const validation = validateRegistrationData(body);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Please check your input and try again.',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Get admin access token
    const adminToken = await getKeycloakAdminToken();
    if (!adminToken) {
      console.error('[REGISTER] Failed to get admin token');
      return NextResponse.json(
        {
          success: false,
          error: 'SERVER_ERROR',
          message: 'Unable to process registration at this time. Please try again later.'
        },
        { status: 500 }
      );
    }

    // Check for existing users
    const existingUser = await checkExistingUser(adminToken, body);
    if (existingUser.exists) {
      return NextResponse.json(
        {
          success: false,
          error: 'USER_EXISTS',
          message: existingUser.message,
          field: existingUser.field
        },
        { status: 409 }
      );
    }

    // Create user in Keycloak
    const createResult = await createKeycloakUser(adminToken, body);
    if (!createResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: createResult.error,
          message: createResult.message
        },
        { status: createResult.status || 500 }
      );
    }

    // Set user password
    const passwordResult = await setUserPassword(adminToken, createResult.userId!, body.password);
    if (!passwordResult.success) {
      // Try to clean up the created user
      await deleteKeycloakUser(adminToken, createResult.userId!);

      return NextResponse.json(
        {
          success: false,
          error: 'PASSWORD_ERROR',
          message: 'Failed to set password. Please try again.'
        },
        { status: 500 }
      );
    }

    console.log(`[REGISTER] Successfully created user: ${body.username} (${createResult.userId})`);

    return NextResponse.json({
      success: true,
      message: 'Account created successfully! You can now sign in.',
      userId: createResult.userId
    });

  } catch (error) {
    console.error('[REGISTER] Unexpected error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'SERVER_ERROR',
        message: 'An unexpected error occurred. Please try again later.'
      },
      { status: 500 }
    );
  }
}

/**
 * Validate registration data
 */
function validateRegistrationData(data: RegisterRequest) {
  const errors: Record<string, string> = {};

  // First name validation
  if (!data.firstName?.trim()) {
    errors.firstName = 'First name is required';
  } else if (data.firstName.trim().length < 2) {
    errors.firstName = 'First name must be at least 2 characters';
  } else if (!/^[a-zA-Z\s]+$/.test(data.firstName.trim())) {
    errors.firstName = 'First name can only contain letters and spaces';
  }

  // Last name validation
  if (!data.lastName?.trim()) {
    errors.lastName = 'Last name is required';
  } else if (data.lastName.trim().length < 2) {
    errors.lastName = 'Last name must be at least 2 characters';
  } else if (!/^[a-zA-Z\s]+$/.test(data.lastName.trim())) {
    errors.lastName = 'Last name can only contain letters and spaces';
  }

  // Email validation
  if (!data.email?.trim()) {
    errors.email = 'Email address is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email.trim())) {
    errors.email = 'Please enter a valid email address';
  }

  // Mobile validation
  if (!data.mobile?.trim()) {
    errors.mobile = 'Mobile number is required';
  } else {
    const cleanMobile = data.mobile.replace(/\D/g, '');
    if (cleanMobile.length !== 10) {
      errors.mobile = 'Please enter a valid 10-digit mobile number';
    }
  }

  // Username validation
  if (!data.username?.trim()) {
    errors.username = 'Username is required';
  } else if (data.username.trim().length < 3) {
    errors.username = 'Username must be at least 3 characters';
  } else if (data.username.trim().length > 50) {
    errors.username = 'Username must be less than 50 characters';
  } else if (!/^[a-zA-Z0-9_]+$/.test(data.username.trim())) {
    errors.username = 'Username can only contain letters, numbers, and underscores';
  }

  // Password validation
  if (!data.password) {
    errors.password = 'Password is required';
  } else if (data.password.length < 8) {
    errors.password = 'Password must be at least 8 characters';
  } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(data.password)) {
    errors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Get Keycloak admin access token
 */
async function getKeycloakAdminToken(): Promise<string | null> {
  try {
    const keycloakIssuer = process.env.KEYCLOAK_ISSUER;
    const clientId = process.env.KEYCLOAK_CLIENT_ID;
    const clientSecret = process.env.KEYCLOAK_CLIENT_SECRET;

    if (!keycloakIssuer || !clientId || !clientSecret) {
      console.error('[REGISTER] Missing Keycloak environment variables');
      return null;
    }

    const tokenUrl = `${keycloakIssuer}/protocol/openid-connect/token`;
    console.log('[REGISTER] Getting admin token from:', tokenUrl);

    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: clientId,
        client_secret: clientSecret,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('[REGISTER] Failed to get admin token:', response.status, response.statusText, errorText);
      return null;
    }

    const data = await response.json();
    console.log('[REGISTER] Successfully obtained admin token');
    return data.access_token;
  } catch (error) {
    console.error('[REGISTER] Error getting admin token:', error);
    return null;
  }
}

/**
 * Check for existing users by username, email, or mobile
 */
async function checkExistingUser(adminToken: string, userData: RegisterRequest) {
  const keycloakIssuer = process.env.KEYCLOAK_ISSUER;
  const realm = process.env.KEYCLOAK_REALM;

  if (!keycloakIssuer || !realm) {
    console.error('[REGISTER] Missing Keycloak environment variables for user check');
    return { exists: false };
  }

  const keycloakBaseUrl = keycloakIssuer.replace(`/realms/${realm}`, '');
  const adminUrl = `${keycloakBaseUrl}/admin/realms/${realm}/users`;

  try {
    // Check username
    console.log('[REGISTER] Checking for existing username:', userData.username);
    const usernameResponse = await fetch(`${adminUrl}?username=${encodeURIComponent(userData.username)}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (usernameResponse.ok) {
      const users = await usernameResponse.json();
      if (users.length > 0) {
        console.log('[REGISTER] Username already exists:', userData.username);
        return {
          exists: true,
          field: 'username',
          message: 'This username is already taken. Please choose a different username.'
        };
      }
    }

    // Check email
    console.log('[REGISTER] Checking for existing email:', userData.email);
    const emailResponse = await fetch(`${adminUrl}?email=${encodeURIComponent(userData.email)}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (emailResponse.ok) {
      const users = await emailResponse.json();
      if (users.length > 0) {
        console.log('[REGISTER] Email already exists:', userData.email);
        return {
          exists: true,
          field: 'email',
          message: 'An account with this email address already exists. Please use a different email or try signing in.'
        };
      }
    }

    // Check mobile (stored in attributes)
    console.log('[REGISTER] Checking for existing mobile:', userData.mobile);
    const mobileResponse = await fetch(`${adminUrl}?q=mobile:${encodeURIComponent(userData.mobile)}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (mobileResponse.ok) {
      const users = await mobileResponse.json();
      const mobileUsers = users.filter((user: any) =>
        user.attributes?.mobile?.[0] === userData.mobile.replace(/\D/g, '')
      );

      if (mobileUsers.length > 0) {
        console.log('[REGISTER] Mobile already exists:', userData.mobile);
        return {
          exists: true,
          field: 'mobile',
          message: 'An account with this mobile number already exists. Please use a different number or try signing in.'
        };
      }
    }

    console.log('[REGISTER] No existing users found');
    return { exists: false };
  } catch (error) {
    console.error('[REGISTER] Error checking existing user:', error);
    // In case of error, allow registration to proceed
    return { exists: false };
  }
}

/**
 * Create user in Keycloak
 */
async function createKeycloakUser(adminToken: string, userData: RegisterRequest) {
  const keycloakIssuer = process.env.KEYCLOAK_ISSUER;
  const realm = process.env.KEYCLOAK_REALM;

  if (!keycloakIssuer || !realm) {
    console.error('[REGISTER] Missing Keycloak environment variables for user creation');
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to create account at this time. Please try again later.'
    };
  }

  const keycloakBaseUrl = keycloakIssuer.replace(`/realms/${realm}`, '');
  const adminUrl = `${keycloakBaseUrl}/admin/realms/${realm}/users`;

  const keycloakUser: KeycloakUser = {
    username: userData.username.trim(),
    email: userData.email.trim().toLowerCase(),
    firstName: userData.firstName.trim(),
    lastName: userData.lastName.trim(),
    enabled: true,
    emailVerified: false, // Require email verification in production
    attributes: {
      mobile: [userData.mobile.replace(/\D/g, '')] // Store only digits
    }
  };

  try {
    console.log('[REGISTER] Creating user in Keycloak:', userData.username);
    const response = await fetch(adminUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(keycloakUser),
    });

    if (response.ok) {
      // Get user ID from Location header
      const location = response.headers.get('Location');
      const userId = location?.split('/').pop();

      console.log('[REGISTER] User created successfully:', userId);
      return {
        success: true,
        userId: userId
      };
    } else {
      const errorData = await response.json().catch(() => ({}));
      console.error('[REGISTER] Failed to create user:', response.status, errorData);

      // Handle specific Keycloak errors
      if (response.status === 409) {
        return {
          success: false,
          error: 'USER_EXISTS',
          message: 'A user with this information already exists.',
          status: 409
        };
      }

      return {
        success: false,
        error: 'CREATE_USER_ERROR',
        message: 'Failed to create user account. Please try again.',
        status: response.status
      };
    }
  } catch (error) {
    console.error('[REGISTER] Error creating user:', error);
    return {
      success: false,
      error: 'SERVER_ERROR',
      message: 'Unable to create account at this time. Please try again later.'
    };
  }
}

/**
 * Set user password in Keycloak
 */
async function setUserPassword(adminToken: string, userId: string, password: string) {
  const keycloakIssuer = process.env.KEYCLOAK_ISSUER;
  const realm = process.env.KEYCLOAK_REALM;

  if (!keycloakIssuer || !realm) {
    console.error('[REGISTER] Missing Keycloak environment variables for password setting');
    return { success: false };
  }

  const keycloakBaseUrl = keycloakIssuer.replace(`/realms/${realm}`, '');
  const passwordUrl = `${keycloakBaseUrl}/admin/realms/${realm}/users/${userId}/reset-password`;

  try {
    console.log('[REGISTER] Setting password for user:', userId);
    const response = await fetch(passwordUrl, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'password',
        value: password,
        temporary: false
      }),
    });

    if (response.ok) {
      console.log('[REGISTER] Password set successfully for user:', userId);
      return { success: true };
    } else {
      const errorText = await response.text();
      console.error('[REGISTER] Failed to set password:', response.status, errorText);
      return { success: false };
    }
  } catch (error) {
    console.error('[REGISTER] Error setting password:', error);
    return { success: false };
  }
}

/**
 * Delete user from Keycloak (cleanup on error)
 */
async function deleteKeycloakUser(adminToken: string, userId: string) {
  const keycloakIssuer = process.env.KEYCLOAK_ISSUER;
  const realm = process.env.KEYCLOAK_REALM;

  if (!keycloakIssuer || !realm) {
    console.error('[REGISTER] Missing Keycloak environment variables for user deletion');
    return;
  }

  const keycloakBaseUrl = keycloakIssuer.replace(`/realms/${realm}`, '');
  const deleteUrl = `${keycloakBaseUrl}/admin/realms/${realm}/users/${userId}`;

  try {
    console.log('[REGISTER] Deleting user during cleanup:', userId);
    await fetch(deleteUrl, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
      },
    });
    console.log('[REGISTER] User deleted successfully during cleanup:', userId);
  } catch (error) {
    console.error('[REGISTER] Error deleting user during cleanup:', error);
  }
}
