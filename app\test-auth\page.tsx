"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthContext, authHelpers } from "@/components/auth/auth-provider";
import { ShoppingCart, Play, CreditCard, User } from "lucide-react";

export default function TestAuthPage() {
  const { requireAuth, isAuthenticated, user } = useAuthContext();

  const testCartAuth = () => {
    requireAuth(authHelpers.requireAuthForCart(
      () => {
        alert("Cart authentication successful!");
      },
      () => {
        console.log("Cart authentication cancelled");
      }
    ));
  };

  const testTrialAuth = () => {
    requireAuth(authHelpers.requireAuthForTrial(
      "Test Product",
      () => {
        alert("Trial authentication successful!");
      },
      () => {
        console.log("Trial authentication cancelled");
      }
    ));
  };

  const testSubscriptionAuth = () => {
    requireAuth(authHelpers.requireAuthForSubscription(
      "Premium Plan",
      () => {
        alert("Subscription authentication successful!");
      },
      () => {
        console.log("Subscription authentication cancelled");
      }
    ));
  };

  const testCheckoutAuth = () => {
    requireAuth(authHelpers.requireAuthForCheckout(
      () => {
        alert("Checkout authentication successful!");
      },
      () => {
        console.log("Checkout authentication cancelled");
      }
    ));
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Authentication System Test</h1>
          <p className="text-muted-foreground">
            Test the new modal-based authentication system with different scenarios
          </p>
        </div>

        {isAuthenticated ? (
          <Card className="mb-8 border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="text-green-800 flex items-center gap-2">
                <User className="h-5 w-5" />
                Authenticated
              </CardTitle>
              <CardDescription className="text-green-600">
                Welcome back, {user?.name || user?.email || 'User'}!
              </CardDescription>
            </CardHeader>
          </Card>
        ) : (
          <Card className="mb-8 border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="text-orange-800">Not Authenticated</CardTitle>
              <CardDescription className="text-orange-600">
                Click any button below to test the authentication modal
              </CardDescription>
            </CardHeader>
          </Card>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Cart Authentication
              </CardTitle>
              <CardDescription>
                Test authentication flow for adding items to cart and checkout
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={testCartAuth} className="w-full">
                Test Cart Auth
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                Trial Authentication
              </CardTitle>
              <CardDescription>
                Test authentication flow for starting product trials
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={testTrialAuth} className="w-full" variant="outline">
                Test Trial Auth
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Subscription Authentication
              </CardTitle>
              <CardDescription>
                Test authentication flow for subscribing to plans
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={testSubscriptionAuth} className="w-full" variant="secondary">
                Test Subscription Auth
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Checkout Authentication
              </CardTitle>
              <CardDescription>
                Test authentication flow for checkout process
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={testCheckoutAuth} className="w-full" variant="destructive">
                Test Checkout Auth
              </Button>
            </CardContent>
          </Card>
        </div>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground space-y-2">
              <p><strong>1.</strong> If you're not authenticated, click any button to see the new modal-based authentication system.</p>
              <p><strong>2.</strong> The modal includes both login and signup tabs with inline forms.</p>
              <p><strong>3.</strong> For testing login, use: <code className="bg-muted px-2 py-1 rounded">9190288431000</code> / <code className="bg-muted px-2 py-1 rounded">919028431000</code></p>
              <p><strong>4.</strong> The system no longer redirects to external Keycloak pages for login.</p>
              <p><strong>5.</strong> All authentication flows now use consistent modal-based UX.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
