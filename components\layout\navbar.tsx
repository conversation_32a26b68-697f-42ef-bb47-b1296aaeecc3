"use client";

import {
  Shopping<PERSON>art,
  <PERSON>u as <PERSON>uIcon,
  Search,
} from "lucide-react";
import React, { useState, useEffect } from "react";
import {
  useSession,
  signOut,
} from "next-auth/react";
import { usePathname } from "next/navigation";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "../ui/sheet";
import { Separator } from "../ui/separator";
import { Button } from "../ui/button";
import { ToggleTheme } from "./toogle-theme";
import Logo from "./logo";
import { routeList } from "@/@data/navbar";
import { useCart } from "@/context/cart-context";
import { MiniCart } from "../cart/mini-cart";
import { Badge } from "../ui/badge";
import { useAuthGuard } from "@/components/auth/auth-guard";
import { ProductNav, getProductMobileMenuItems } from "../ui/product-nav";
import { MainNav, getMainMobileMenuItems } from "../ui/main-nav";
import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";
import type { ProductResponse } from "../../@types/product";
import { SearchBar } from "../SearchBar";

const GET_PRODUCTS = gql`
  query GetProducts {
    products {
      slug
      ProductCard {
        productName
        productDescription
        productLogo {
          url
        }
      }
    }
  }
`;

// Define product page sections that can be targeted
const productSections = [
  { id: "benefits", label: "Benefits" },
  { id: "features", label: "Features" },
  { id: "services", label: "Services" },
  { id: "pricing", label: "Pricing" },
  // { id: "testimonials", label: "Testimonials" },
  { id: "contact", label: "Contact" },
  { id: "faq", label: "FAQ" }
];

export const Navbar = () => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [activeItem, setActiveItem] = React.useState<string | null>(null);
  const { data: session } = useSession();
  const { setIsOpen: setCartOpen, totalItems } = useCart();
  const { requireAuth, AuthModalComponent } = useAuthGuard();
  const pathname = usePathname();
  const [products, setProducts] = useState<ProductResponse[]>([]);
  const [loading, setLoading] = useState(true);

  // Custom logout handler to ensure clean logout
  const handleLogout = async () => {
    // Client-side cleanup before calling signOut
    try {
      // Clear any session/local storage items
      localStorage.removeItem('keycloak-token');
      sessionStorage.removeItem('keycloak-token');

      // Clear all cookies with path='/'
      document.cookie.split(';').forEach(cookie => {
        const [name] = cookie.trim().split('=');
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`;
      });

      // Perform NextAuth signOut with redirect to home page
      await signOut({ callbackUrl: '/' });
    } catch (error) {
      console.error("Error during logout:", error);
      // Fallback to basic signOut if cleanup fails
      signOut();
    }
  };

  // Custom login handler using modal-based authentication
  const handleLogin = () => {
    // Close mobile menu if open
    if (isOpen) setIsOpen(false);

    // Use the new modal-based authentication system for seamless UX
    requireAuth({
      message: "Welcome! Please sign in to access your account",
      onSuccess: () => {
        // User is now authenticated, page will refresh automatically
        console.log("✅ NAVBAR - User successfully authenticated");
      },
      onCancel: () => {
        console.log("🔍 NAVBAR - User cancelled authentication");
      }
    });
  };

  // Get only the first name of the user
  const firstName = session?.user?.name?.split(' ')[0] || '';

  // Determine if we're on a product detail page
  const isProductDetailPage = pathname.startsWith('/products/') && pathname !== '/products';

  // Add section identifiers to elements that might not have them
  useEffect(() => {
    // Only run on product detail pages
    if (!isProductDetailPage) return;

    // Wait for content to be fully loaded
    setTimeout(() => {
      try {
        // Try to add IDs to sections that don't have them for easier targeting
        const pricingSection = document.querySelector('[ref="pricingSectionRef"]') ||
                           document.querySelector('div:has(.PricingSection)');
        if (pricingSection && !pricingSection.id) {
          pricingSection.id = 'pricing';
        }

        // Check for BenefitsSection and add ID if missing
        const benefitsSection = document.querySelector('section:has(.BenefitsSection)') ||
                             document.querySelector('.benefits-section');
        if (benefitsSection && !benefitsSection.id) {
          benefitsSection.id = 'benefits';
        }

        // Check for FeaturesSection and add ID if missing
        const featuresSection = document.querySelector('section:has(.FeaturesSection)') ||
                             document.querySelector('.features-section');
        if (featuresSection && !featuresSection.id) {
          featuresSection.id = 'features';
        }

        // Check for ServicesSection and add ID if missing
        const servicesSection = document.querySelector('section:has(.ServicesSection)') ||
                             document.querySelector('.services-section');
        if (servicesSection && !servicesSection.id) {
          servicesSection.id = 'services';
        }

        // Check for TestimonialSection and add ID if missing
        const testimonialSection = document.querySelector('section:has(.TestimonialSection)') ||
                               document.querySelector('.testimonial-section');
        if (testimonialSection && !testimonialSection.id) {
          testimonialSection.id = 'testimonials';
        }

        // Check for FAQSection and add ID if missing
        const faqSection = document.querySelector('section:has(.FAQSection)') ||
                        document.querySelector('.faq-section');
        if (faqSection && !faqSection.id) {
          faqSection.id = 'faq';
        }

        // Check for ContactSection and add ID if missing
        const contactSection = document.querySelector('section:has(.ContactSection)') ||
                            document.querySelector('.contact-section');
        if (contactSection && !contactSection.id) {
          contactSection.id = 'contact';
        }
      } catch (error) {
        console.error("Error setting up section IDs:", error);
      }
    }, 1000); // Wait for a second to ensure the page is fully rendered
  }, [isProductDetailPage, pathname]);

  // Helper function to scroll to a section
  const scrollToSection = (sectionId: string) => {
    try {
      // Define a proper window interface with optional scroll function
      const customWindow = window as Window & { scrollToPricing?: () => void };

      // Special case for pricing - try to use the direct scrollToPricing function if available
      if (sectionId === 'pricing' && typeof customWindow.scrollToPricing === 'function') {
        customWindow.scrollToPricing();
        return true;
      }

      // Use a small delay to ensure DOM is fully rendered
      setTimeout(() => {
        // Find the section element
        let targetElement: HTMLElement | null = null;

        // Map section IDs to potential selectors with corresponding component types
        const sectionMap: Record<string, {selectors: string[], componentName: string}> = {
          'benefits': {
            selectors: ['#benefits', '.benefits-section', '[data-section="benefits"]'],
            componentName: 'BenefitsSection'
          },
          'features': {
            selectors: ['#features', '.features-section', '[data-section="features"]'],
            componentName: 'FeaturesSection'
          },
          'services': {
            selectors: ['#services', '.services-section', '[data-section="services"]'],
            componentName: 'ServicesSection'
          },
          'pricing': {
            selectors: ['#pricing', '.pricing-section', '[data-section="pricing"]', '[ref="pricingSectionRef"]'],
            componentName: 'PricingSection'
          },
          'testimonials': {
            selectors: ['#testimonials', '.testimonials-section', '[data-section="testimonials"]'],
            componentName: 'TestimonialSection'
          },
          'contact': {
            selectors: ['#contact', '.contact-section', '[data-section="contact"]'],
            componentName: 'ContactSection'
          },
          'faq': {
            selectors: ['#faq', '.faq-section', '[data-section="faq"]'],
            componentName: 'FAQSection'
          }
        };

        // First, try direct selector matching
        if (sectionMap[sectionId]) {
          // Try basic selectors first
          for (const selector of sectionMap[sectionId].selectors) {
            const element = document.querySelector(selector);
            if (element) {
              targetElement = element as HTMLElement;
              break;
            }
          }

          // If not found, try looking for the component by class name
          if (!targetElement) {
            const componentName = sectionMap[sectionId].componentName;

            // Try different selector patterns
            const componentSelectors = [
              `.${componentName}`,
              `[class*="${componentName}"]`,
              `div:has(.${componentName})`,
              `section:has(.${componentName})`
            ];

            for (const selector of componentSelectors) {
              try {
                const element = document.querySelector(selector);
                if (element) {
                  targetElement = element.closest('section') as HTMLElement ||
                                element.closest('div') as HTMLElement ||
                                element as HTMLElement;
                  break;
                }
              } catch {
                // Some selectors might not be supported
                continue;
              }
            }
          }
        }

        // Try to find by component name
        if (!targetElement && sectionMap[sectionId]) {
          const componentName = sectionMap[sectionId].componentName;

          // Special case for pricing section
          if (sectionId === 'pricing') {
            const pricingRef = document.querySelector('[ref="pricingSectionRef"]');
            if (pricingRef) {
              targetElement = pricingRef as HTMLElement;
            }
          }

          // Find by innerHTML containing component name
          if (!targetElement) {
            // Use regular for loop to avoid iterator issues
            const allElements = document.querySelectorAll('div, section');
            for (let i = 0; i < allElements.length; i++) {
              const element = allElements[i];
              if (element.innerHTML.includes(componentName)) {
                targetElement = element as HTMLElement;
                break;
              }
            }
          }
        }

        // Find by heading text
        if (!targetElement) {
          // Use regular for loop to avoid iterator issues
          const headings = document.querySelectorAll('h1, h2, h3, h4');
          const sectionLower = sectionId.toLowerCase();
          const sectionCapitalized = sectionId.charAt(0).toUpperCase() + sectionId.slice(1);

          for (let i = 0; i < headings.length; i++) {
            const heading = headings[i];
            const headingText = heading.textContent?.toLowerCase() || '';

            if (headingText.includes(sectionLower) || headingText.includes(sectionCapitalized)) {
              targetElement = heading.closest('section') as HTMLElement ||
                            heading.closest('div') as HTMLElement ||
                            heading.parentElement as HTMLElement;
              if (targetElement) break;
            }
          }
        }

        if (targetElement) {
          // Scroll to the section with smooth behavior
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });

          // Add visual indication
          setTimeout(() => {
            if (targetElement) {
              targetElement.classList.add('highlight-section');

              // Remove highlight after animation
              setTimeout(() => {
                if (targetElement) {
                  targetElement.classList.remove('highlight-section');
                }
              }, 2000);
            }
          }, 500);

          return true;
        } else {
          // Final fallback - try to find div by brute force search
          if (sectionId === 'pricing') {
            // Try to find any div that contains PricingSection text
            const allDivs = document.querySelectorAll('div');
            for (let i = 0; i < allDivs.length; i++) {
              if (allDivs[i].innerHTML.includes('PricingSection') ||
                  allDivs[i].innerHTML.toLowerCase().includes('pricing')) {
                allDivs[i].scrollIntoView({ behavior: 'smooth', block: 'start' });
                return true;
              }
            }
          }

          console.warn(`Could not find section with ID: ${sectionId}`);
          return false;
        }
      }, 100); // Small delay to ensure DOM is ready

      // Return true to indicate we're trying to scroll (even if asynchronous)
      return true;
    } catch (error) {
      console.error(`Error scrolling to section ${sectionId}:`, error);
      return false;
    }
  };

  useEffect(() => {
    const loadProducts = async () => {
      try {
        const { data } = await client.query({
          query: GET_PRODUCTS,
          fetchPolicy: "no-cache",
        });
        setProducts(data.products);
      } catch (error) {
        console.error('Failed to load products:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  // Handler to check if we should open the cart
  const handleCartClick = () => {
    // Don't open cart if we're on the checkout page
    if (pathname !== "/checkout") {
      setCartOpen(true);
    }
  };

  // Get mobile menu items
  const getMobileMenuItems = () => {
    if (isProductDetailPage) {
      return getProductMobileMenuItems(
        productSections,
        routeList,
        (sectionId) => {
          setTimeout(() => scrollToSection(sectionId), 300);
        },
        () => setIsOpen(false)
      );
    } else {
      return getMainMobileMenuItems(
        routeList,
        () => setIsOpen(false)
      );
    }
  };

  // Return the JSX structure correctly
  return (
    <>
      <style jsx global>{`
        /* Animation for highlighting sections when scrolled to */
        @keyframes highlight-pulse {
          0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
          70% { box-shadow: 0 0 0 15px rgba(239, 68, 68, 0); }
          100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
        }

        .highlight-section {
          animation: highlight-pulse 2s ease-out 1;
          position: relative;
          z-index: 1;
        }
      `}</style>

      <header className="sticky top-2 lg:top-5 z-40">
        <div className="container">
          <div className="bg-opacity-15 border rounded-2xl flex items-center h-14 px-3 bg-background/70 backdrop-blur-sm">
            {/* Left section - Logo */}
            <div className="flex-shrink-0 w-[150px]">
              <Logo />
            </div>

            {/* Middle section - Main Navigation - With fixed positioning */}
            <div className="hidden lg:flex flex-grow justify-center h-full">
              <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center h-full">
                {isProductDetailPage ? (
                  <ProductNav
                    activeItem={activeItem}
                    setActiveItem={setActiveItem}
                    products={products}
                    loading={loading}
                    productSections={productSections}
                    routeList={routeList}
                    scrollToSection={scrollToSection}
                  />
                ) : (
                  <MainNav
                    activeItem={activeItem}
                    setActiveItem={setActiveItem}
                    products={products}
                    loading={loading}
                    routeList={routeList}
                  />
                )}
              </div>
            </div>

            {/* Right section - Actions */}
            <div className="flex items-center gap-2 ml-auto justify-end">
              {/* Search Bar */}
              <div className="hidden lg:flex items-center mr-2">
                <SearchBar />
              </div>

              {/* Toggle Theme */}
              <div className="hidden lg:flex w-10 h-10 items-center justify-center">
                <ToggleTheme />
              </div>

              {/* Cart Button */}
              <Button
                variant="ghost"
                size="icon"
                aria-label="Shopping Cart"
                className="hidden lg:flex w-10 h-10 relative pr-3 pl-3"
                onClick={handleCartClick}
              >
                <ShoppingCart className="size-5" />
                {totalItems > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 h-5 min-w-5 p-0 flex items-center justify-center rounded-full text-[10px]"
                  >
                    {totalItems}
                  </Badge>
                )}
              </Button>

              {/* Session Management */}
              <div className="hidden lg:flex items-center">
                {!session ? (
                  <Button
                    onClick={handleLogin}
                    size="sm"
                    className="whitespace-nowrap"
                    aria-label="Login"
                  >
                    Login
                  </Button>
                ) : (
                  <div className="flex items-center">
                    <div className="text-sm whitespace-nowrap mr-2">
                      Hello, {firstName}
                    </div>
                    <Button
                      onClick={handleLogout}
                      size="sm"
                      className="whitespace-nowrap"
                      variant="destructive"
                      aria-label="Logout"
                    >
                      Logout
                    </Button>
                  </div>
                )}
              </div>

              {/* Mobile Controls */}
              <div className="flex items-center lg:hidden">
                {/* Cart Button for Mobile */}
                <Button
                  variant="ghost"
                  size="icon"
                  aria-label="Shopping Cart"
                  className="relative"
                  onClick={handleCartClick}
                >
                  <ShoppingCart className="size-5" />
                  {totalItems > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-1 -right-1 h-5 min-w-5 p-0 flex items-center justify-center rounded-full text-[10px]"
                    >
                      {totalItems}
                    </Badge>
                  )}
                </Button>

                {/* Mobile Menu */}
                <Sheet
                  open={isOpen}
                  onOpenChange={setIsOpen}
                >
                  <SheetTrigger asChild>
                    <MenuIcon
                      onClick={() => setIsOpen(!isOpen)}
                      className="cursor-pointer ml-2"
                    />
                  </SheetTrigger>

                  <SheetContent
                    side="left"
                    className="flex flex-col justify-between rounded-tr-2xl rounded-br-2xl bg-card border-secondary"
                  >
                    <div>
                      <SheetHeader className="mb-4 ml-4">
                        <SheetTitle className="flex items-center">
                          <Logo />
                        </SheetTitle>
                      </SheetHeader>

                      <div className="flex flex-col gap-2">
                        {/* Mobile menu items */}
                        <Button
                          onClick={() => {
                            setIsOpen(false);
                            window.location.href = "/products";
                          }}
                          variant="ghost"
                          className="justify-start text-base"
                        >
                          Products
                        </Button>

                        {getMobileMenuItems()}

                        {/* Mobile Search */}
                        <div className="px-2 py-2">
                          <Button
                            variant="outline"
                            className="w-full justify-start"
                            onClick={() => {
                              setIsOpen(false);
                              // Open search dialog
                              const searchEvent = new CustomEvent('open-global-search');
                              document.dispatchEvent(searchEvent);
                            }}
                          >
                            <Search className="mr-2 h-4 w-4" />
                            Search
                          </Button>
                        </div>

                        {!session ? (
                          <Button
                            onClick={handleLogin}
                            size="sm"
                            className="ms-2"
                            aria-label="Login"
                          >
                            Login
                          </Button>
                        ) : (
                          <>
                            <div className="ms-2 text-sm">
                              Hello, {firstName}
                            </div>
                            <Button
                              onClick={handleLogout}
                              size="sm"
                              className="ms-2"
                              variant="destructive"
                              aria-label="Logout"
                            >
                              Logout
                            </Button>
                          </>
                        )}
                      </div>
                    </div>

                    <SheetFooter className="flex-col sm:flex-col justify-start items-start">
                      <Separator className="mb-2" />
                      <ToggleTheme />
                    </SheetFooter>
                  </SheetContent>
                </Sheet>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mini Cart Component */}
      <MiniCart />

      {/* Auth Modal for seamless authentication */}
      <AuthModalComponent />
    </>
  );
};
