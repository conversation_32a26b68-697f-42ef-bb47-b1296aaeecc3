/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosError } from "axios";
import KeycloakProvider from "next-auth/providers/keycloak";
import CredentialsProvider from "next-auth/providers/credentials";
import type { NextAuthOptions } from "next-auth";
import type { JWT } from "next-auth/jwt";

// Ensure all environment variables are defined
const {
  K<PERSON><PERSON>CLOAK_CLIENT_ID,
  KEYCLOAK_CLIENT_SECRET,
  KEYCLOAK_ISSUER,
  NEXTAUTH_SECRET,
  NEXTAUTH_URL,
  NODE_ENV,
  COOKIE_DOMAIN
} = process.env;

const REQUIRED_ENV_VARS = [
  'KEYCLOAK_CLIENT_ID',
  'KEYCLOAK_CLIENT_SECRET',
  'KEYCLOAK_ISSUER',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL'
];

// Validate all required environment variables
REQUIRED_ENV_VARS.forEach(varName => {
  if (!process.env[varName]) {
    throw new Error(`Missing required environment variable: ${varName}`);
  }
});

const isProduction = NODE_ENV === 'production';
const isHttps = NEXTAUTH_URL?.startsWith('https://');
const isLocalhost = NEXTAUTH_URL?.includes('localhost');
// Only use secure cookies in production AND on HTTPS (not on localhost)
const useSecureCookie = isProduction && isHttps && !isLocalhost;

// For development mode, ensure we can test production builds locally
const isDevelopment = NODE_ENV === 'development' || process.env.NODE_ENV === 'development';

// Log environment info for debugging
if (isDevelopment || !isProduction || isLocalhost) {
  console.log('[AUTH] Environment info:', {
    NODE_ENV,
    isProduction,
    isHttps,
    isLocalhost,
    useSecureCookie,
    NEXTAUTH_URL
  });

  // Log cookie domain logic
  const cookieDomain = (() => {
    if (NEXTAUTH_URL?.includes('localhost')) {
      return undefined; // No domain for localhost
    }
    if (NEXTAUTH_URL?.includes('cubeone.in')) {
      return '.cubeone.in'; // Allow all cubeone.in subdomains
    }
    return COOKIE_DOMAIN || undefined; // Fallback to env variable
  })();
  
  console.log('[AUTH] Cookie domain:', cookieDomain || 'undefined (localhost)');
}

// Initialize Keycloak Provider with refresh token options
const keycloak = KeycloakProvider({
  clientId: KEYCLOAK_CLIENT_ID!,
  clientSecret: KEYCLOAK_CLIENT_SECRET!,
  issuer: KEYCLOAK_ISSUER!,
  // Enable token refresh
  authorization: {
    params: {
      scope: 'openid email profile',
      prompt: 'consent'
    }
  }
});

// Handle Keycloak post-logout handshake
async function doFinalSignoutHandshake(jwt: JWT) {
  if (jwt.provider !== keycloak.id || !jwt.id_token) return;

  try {
    const params = new URLSearchParams();
    params.append('id_token_hint', jwt.id_token as string);
    if (NEXTAUTH_URL) {
      params.append('post_logout_redirect_uri', NEXTAUTH_URL);
    }

    // Force Keycloak to invalidate the session completely
    params.append('invalidate_session', 'true');

    const { status, statusText } = await axios.get(
      `${KEYCLOAK_ISSUER}/protocol/openid-connect/logout?${params.toString()}`,
      { validateStatus: status => status < 500 } // Accept 2xx/3xx/4xx as valid responses
    );
    console.log("Completed post-logout handshake:", status, statusText);

    // Attempt to invalidate refresh tokens by making a token revocation request
    try {
      const revokeParams = new URLSearchParams({
        client_id: KEYCLOAK_CLIENT_ID!,
        client_secret: KEYCLOAK_CLIENT_SECRET!,
        token_type_hint: 'refresh_token',
        token: jwt.refreshToken || ''
      });

      await axios.post(
        `${KEYCLOAK_ISSUER}/protocol/openid-connect/revoke`,
        revokeParams.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          validateStatus: status => status < 500
        }
      );
    } catch (revokeError) {
      console.warn("Token revocation failed:", (revokeError as AxiosError)?.message || revokeError);
    }
  } catch (error) {
    console.error("Post-logout handshake failed:", (error as AxiosError)?.message || error);
  }
}

// Refresh access token when expired
async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    // Validate refresh token
    if (!token.refreshToken) {
      console.warn("No refresh token available for token refresh");
      return {
        ...token,
        error: "RefreshAccessTokenError",
      };
    }

    // Check if token is already marked with an error
    if (token.error) {
      console.warn(`Token already has error: ${token.error}, skipping refresh attempt`);
      return token;
    }

    const tokenEndpoint = `${KEYCLOAK_ISSUER}/protocol/openid-connect/token`;
    const params = new URLSearchParams({
      client_id: KEYCLOAK_CLIENT_ID!,
      client_secret: KEYCLOAK_CLIENT_SECRET!,
      grant_type: 'refresh_token',
      refresh_token: token.refreshToken as string
    });

    console.log(`Attempting to refresh token for user: ${token.name || 'unknown'}`);

    const response = await axios.post(tokenEndpoint, params.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 10000, // 10 second timeout
      validateStatus: (status) => status === 200 // Only 200 is valid
    });

    // Validate response
    if (!response.data || !response.data.access_token) {
      console.error("Invalid token refresh response: missing access_token");
      throw new Error("Invalid token refresh response");
    }

    const refreshedTokens = response.data;
    console.log(`Token refreshed successfully for user: ${token.name || 'unknown'}`);

    // Calculate new expiry time - add a small buffer to account for processing time
    const expiresIn = refreshedTokens.expires_in || 300; // Default to 5 minutes if not specified
    const expiryTime = Date.now() + (expiresIn * 1000) - 30000; // Subtract 30 seconds as buffer

    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,
      accessTokenExpires: expiryTime,
      id_token: refreshedTokens.id_token ?? token.id_token,
      error: undefined, // Clear any previous errors
    };
  } catch (error) {
    // Enhanced error logging
    const axiosError = error as AxiosError;
    if (axiosError.response) {
      // The request was made and the server responded with a status code outside of 2xx
      console.error(`Token refresh failed with status: ${axiosError.response.status}`);
      console.error(`Response data:`, axiosError.response.data);
    } else if (axiosError.request) {
      // The request was made but no response was received
      console.error(`Token refresh failed - no response received:`, axiosError.message);
    } else {
      // Something happened in setting up the request
      console.error(`Token refresh error:`, axiosError.message);
    }

    // Return original token with error flag
    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

// Function to fetch user profile from Keycloak
async function fetchUserProfile(accessToken: string) {
  try {
    const response = await axios.get(
      `${KEYCLOAK_ISSUER}/protocol/openid-connect/userinfo`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        timeout: 5000, // 5 second timeout
        validateStatus: status => status === 200 // Only 200 is valid
      }
    );
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response) {
      console.error(`Failed to fetch user profile: ${axiosError.response.status} ${axiosError.response.statusText}`);
    } else if (axiosError.request) {
      console.error("Failed to fetch user profile: No response received");
    } else {
      console.error("Failed to fetch user profile:", axiosError.message);
    }
    return null;
  }
}

// Extend the User type to include profile fields
declare module "next-auth" {
  interface User {
    phone?: string;
    email?: string;
    given_name?: string;
    family_name?: string;
    preferred_username?: string;
    roles?: string[];
    profile?: unknown;
  }
  interface Session {
    accessToken?: string;
    error?: string;
    phone?: string;
    email?: string;
    given_name?: string;
    family_name?: string;
    preferred_username?: string;
    roles?: string[];
    profile?: unknown;
    expires: string;
    accessTokenExpires?: number;
  }
}

// Extend JWT to include profile information
declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    accessTokenExpires?: number;
    phone?: string;
    email?: string;
    given_name?: string;
    family_name?: string;
    preferred_username?: string;
    roles?: string[];
    profile?: unknown;
    error?: string;
    isLoggedOut?: boolean;
    lastRefreshAttempt?: number; // Timestamp of last refresh attempt
  }
}

// Optimize token size to prevent cookie size issues
function optimizeTokenSize(token: JWT): JWT {
  // Create a new object with only the absolute minimum necessary fields
  const optimizedToken: JWT = {
    // Essential authentication fields only
    sub: token.sub,
    accessToken: token.accessToken,
    refreshToken: token.refreshToken,
    accessTokenExpires: token.accessTokenExpires,
    provider: token.provider,
    isLoggedOut: token.isLoggedOut,
    error: token.error,
    lastRefreshAttempt: token.lastRefreshAttempt,

    // Minimal user identity fields
    name: token.name,
    email: token.email,
  };

  // Measure token size
  const tokenSize = JSON.stringify(optimizedToken).length;

  // Log token size for monitoring
  console.log(`Token size: ${tokenSize} bytes`);

  // If token is still too large, aggressively reduce size further
  if (tokenSize > 2500) {
    console.warn(`Token size (${tokenSize} bytes) is approaching limit, applying aggressive size reduction`);

    // Truncate token values if they're too long
    if (optimizedToken.accessToken && optimizedToken.accessToken.length > 500) {
      optimizedToken.accessToken = optimizedToken.accessToken.substring(0, 500);
    }

    if (optimizedToken.refreshToken && optimizedToken.refreshToken.length > 500) {
      optimizedToken.refreshToken = optimizedToken.refreshToken.substring(0, 500);
    }

    // Remove id_token completely as it's usually the largest part
    // We'll still keep the access token for API calls
    delete optimizedToken.id_token;

    // Log new size after optimization
    const newSize = JSON.stringify(optimizedToken).length;
    console.log(`Reduced token size from ${tokenSize} to ${newSize} bytes`);
  }

  // Return the optimized token
  return optimizedToken;
}

// Authentication configuration
export const authOptions: NextAuthOptions = {
  providers: [
    keycloak,
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        // Development logging
        if (isDevelopment || !isProduction) {
          console.log(`[DEV] Credentials login attempt for username: ${credentials.username}`);
        }

        try {
          /*
            Use Keycloak's Resource-Owner-Password (ROP) flow to verify the
            username/password pair and retrieve access / refresh / id tokens.

            Although ROP is generally discouraged for public clients, it is a
            pragmatic fallback here because the credentials are collected on a
            **first-party** form and sent directly to our API route (never to
            the browser).
          */

          const params = new URLSearchParams({
            grant_type: 'password',
            client_id: KEYCLOAK_CLIENT_ID!,
            client_secret: KEYCLOAK_CLIENT_SECRET!,
            username: credentials.username,
            password: credentials.password,
            scope: 'openid email profile'
          });

          const tokenEndpoint = `${KEYCLOAK_ISSUER}/protocol/openid-connect/token`;

          // Development logging
          if (isDevelopment || !isProduction) {
            console.log(`[DEV] Making ROP request to: ${tokenEndpoint}`);
            console.log(`[DEV] Request params:`, { 
              grant_type: 'password',
              client_id: KEYCLOAK_CLIENT_ID,
              username: credentials.username,
              scope: 'openid email profile'
              // Don't log password or client_secret
            });
          }

          const tokenRes = await fetch(tokenEndpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: params.toString(),
          });

          if (!tokenRes.ok) {
            const errorText = await tokenRes.text();
            console.error('Keycloak ROP login failed:', tokenRes.status, tokenRes.statusText);
            if (isDevelopment || !isProduction) {
              console.error(`[DEV] Error response body:`, errorText);
            }
            return null;
          }

          const tokenJson = await tokenRes.json();

          // Development logging
          if (isDevelopment || !isProduction) {
            console.log(`[DEV] Successfully received tokens from Keycloak`);
            console.log(`[DEV] Token response keys:`, Object.keys(tokenJson));
          }

          // id_token contains the user sub; decode its payload safely
          let sub: string | undefined;
          if (tokenJson.id_token) {
            try {
              const payload = JSON.parse(Buffer.from(tokenJson.id_token.split('.')[1], 'base64').toString('utf8'));
              sub = payload.sub;
              if (isDevelopment || !isProduction) {
                console.log(`[DEV] Decoded user ID from id_token: ${sub}`);
              }
            } catch (e) {
              console.warn('Failed to decode id_token:', e);
            }
          }

          // Fallback to username if sub missing
          const userId = sub || credentials.username;

          // Fetch minimal userinfo to get name/email (optional)
          let name: string | undefined;
          let email: string | undefined;
          try {
            const userInfoRes = await fetch(`${KEYCLOAK_ISSUER}/protocol/openid-connect/userinfo`, {
              headers: { Authorization: `Bearer ${tokenJson.access_token}` },
            });
            if (userInfoRes.ok) {
              const info = await userInfoRes.json();
              name = info.name;
              email = info.email;
              if (isDevelopment || !isProduction) {
                console.log(`[DEV] Fetched user info:`, { name, email, sub: info.sub });
              }
            }
          } catch {}

          if (isDevelopment || !isProduction) {
            console.log(`[DEV] Returning user object with ID: ${userId}`);
          }

          return {
            id: userId,
            name: name || credentials.username,
            email,
            accessToken: tokenJson.access_token,
            refreshToken: tokenJson.refresh_token,
            id_token: tokenJson.id_token,
          } as unknown as any; // Allow additional props
        } catch (error) {
          console.error('Credentials authentication failed:', error);
          if (isDevelopment || !isProduction) {
            console.error(`[DEV] Full error details:`, error);
          }
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
    // Session will last for 12 hours by default
    maxAge: 12 * 60 * 60,
  },
  secret: NEXTAUTH_SECRET,
  pages: {
    error: "/auth/error",
  },
  // Enable debug in development or when not in production, or when testing on localhost
  debug: isDevelopment || !isProduction || isLocalhost,
  // Cookie handling with optimized settings for large tokens
  // In development mode (NODE_ENV=development) or when running on http://localhost, cookies are not secure
  // In production mode on HTTPS, cookies are secure
  cookies: {
    sessionToken: {
      name: `${useSecureCookie ? '__Secure-' : ''}next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: useSecureCookie, // false for development/localhost, true for production HTTPS
        maxAge: 12 * 60 * 60, // 12 hours
        // Smart domain handling: no domain for localhost, .cubeone.in for production
        domain: (() => {
          if (NEXTAUTH_URL?.includes('localhost')) {
            return undefined; // No domain for localhost
          }
          if (NEXTAUTH_URL?.includes('cubeone.in')) {
            return '.cubeone.in'; // Allow all cubeone.in subdomains
          }
          return COOKIE_DOMAIN || undefined; // Fallback to env variable
        })(),
      },
    },
    callbackUrl: {
      name: `${useSecureCookie ? '__Secure-' : ''}next-auth.callback-url`,
      options: {
        httpOnly: false, // Allow client-side access for callback URL
        sameSite: "lax",
        path: "/",
        secure: useSecureCookie, // false for development/localhost, true for production HTTPS
        // Smart domain handling: no domain for localhost, .cubeone.in for production
        domain: (() => {
          if (NEXTAUTH_URL?.includes('localhost')) {
            return undefined; // No domain for localhost
          }
          if (NEXTAUTH_URL?.includes('cubeone.in')) {
            return '.cubeone.in'; // Allow all cubeone.in subdomains
          }
          return COOKIE_DOMAIN || undefined; // Fallback to env variable
        })(),
      },
    },
    csrfToken: {
      name: `${useSecureCookie ? '__Host-' : ''}next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: useSecureCookie, // false for development/localhost, true for production HTTPS
        // Smart domain handling: no domain for localhost, .cubeone.in for production
        domain: (() => {
          if (NEXTAUTH_URL?.includes('localhost')) {
            return undefined; // No domain for localhost
          }
          if (NEXTAUTH_URL?.includes('cubeone.in')) {
            return '.cubeone.in'; // Allow all cubeone.in subdomains
          }
          return COOKIE_DOMAIN || undefined; // Fallback to env variable
        })(),
      },
    },
  },
  // Enable JWT chunking to handle large tokens
  jwt: {
    // Maximum token size in bytes before chunking
    maxAge: 12 * 60 * 60, // 12 hours in seconds
  },
  callbacks: {
    async jwt({ token, account, user, trigger }) {
      // Handle sign-out event in a type-safe way
      // NextAuth.js has a bug in the type declaration for the trigger parameter
      // It can actually be "signOut" even though types don't show it
      if (trigger === "signOut" as unknown as string) {
        // Mark the token as logged out
        return { ...token, isLoggedOut: true };
      }

      // Initial sign in
      if (account && account.access_token) {
        // Clear any previous logout flag
        token.isLoggedOut = false;

        // Store only essential token information
        token.accessToken = account.access_token;
        token.refreshToken = account.refresh_token;
        token.accessTokenExpires = account.expires_at ? account.expires_at * 1000 : undefined;
        token.provider = account.provider;

        // Fetch user profile from Keycloak but only store minimal data
        const userProfile = await fetchUserProfile(account.access_token);
        if (userProfile) {
          // Store only essential profile information including the critical user ID
          token.sub = userProfile.sub; // This is the Keycloak user ID - CRITICAL for cart functionality
          token.email = userProfile.email;
          token.name = userProfile.name || `${userProfile.given_name || ''} ${userProfile.family_name || ''}`.trim();

          console.log(`✅ JWT - Stored Keycloak user ID: ${userProfile.sub}`);

          // Don't store the full profile object
          // Don't store roles array
          // Don't store additional fields like phone, given_name, family_name, etc.
        }
      } else if (user) {
        // Support CredentialsProvider (Keycloak ROP)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        token.sub = (user as any).id;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        token.name = (user as any).name;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        token.email = (user as any).email;

        if ((user as any).accessToken) {
          token.accessToken = (user as any).accessToken;
          token.refreshToken = (user as any).refreshToken;
          token.id_token = (user as any).id_token;
          token.accessTokenExpires = Date.now() + 60 * 60 * 1000; // 1h guess
        }
      }

      // Check if token is marked as logged out
      if (token.isLoggedOut) {
        return { ...token, error: "UserLoggedOut" };
      }

      // Handle existing refresh errors
      if (token.error === "RefreshAccessTokenError") {
        console.log(`Token has refresh error for user: ${token.name || 'unknown'}, attempting recovery`);

        // Check if we should attempt recovery
        const lastRefreshAttempt = token.lastRefreshAttempt || 0;
        const now = Date.now();
        const timeSinceLastAttempt = now - lastRefreshAttempt;

        // Only try to recover once every 5 minutes to avoid hammering the server
        if (timeSinceLastAttempt > 5 * 60 * 1000) {
          console.log(`Attempting refresh token recovery for user: ${token.name || 'unknown'}`);
          // Clear the error and try again
          const tokenWithoutError = {
            ...token,
            error: undefined,
            lastRefreshAttempt: now
          };

          // Attempt to refresh the token
          const refreshedToken = await refreshAccessToken(tokenWithoutError);
          return optimizeTokenSize(refreshedToken);
        } else {
          // Not time to retry yet, keep the error
          console.log(`Skipping refresh recovery, last attempt was ${Math.round(timeSinceLastAttempt/1000)}s ago`);
          return optimizeTokenSize(token);
        }
      }

      // If token is expired and we have a refresh token, try to refresh
      const shouldRefreshTime = Math.round((token.accessTokenExpires || 0) - Date.now());

      // If token is valid for less than 1 minute, refresh it
      if (shouldRefreshTime < 60 * 1000 && token.refreshToken) {
        console.log(`Token expires in ${shouldRefreshTime}ms, refreshing for user: ${token.name || 'unknown'}`);
        const refreshedToken = await refreshAccessToken(token);

        // Add timestamp of this refresh attempt
        refreshedToken.lastRefreshAttempt = Date.now();

        return optimizeTokenSize(refreshedToken);
      }

      // Apply size optimization to prevent cookie size issues
      return optimizeTokenSize(token);
    },
    async session({ session, token }) {
      // Log session creation for debugging
      if (isDevelopment || !isProduction) {
        console.log(`[DEV] Session callback triggered for user: ${token.name || 'unknown'}`);
        console.log(`[DEV] Token exists:`, !!token);
        console.log(`[DEV] Token sub:`, token.sub);
        console.log(`[DEV] Token error:`, token.error);
      }

      // Don't create a session if the user is logged out
      if (token.isLoggedOut) {
        // Return a session that will be considered invalid
        if (isDevelopment || !isProduction) {
          console.log(`[DEV] User is logged out, returning invalid session`);
        }
        return { ...session, error: "UserLoggedOut", expires: new Date(0).toISOString() };
      }

      // Handle refresh token errors
      if (token.error === "RefreshAccessTokenError") {
        // Log the error but don't invalidate the session immediately
        console.warn(`Session has RefreshAccessTokenError for user: ${token.name || 'unknown'}`);

        // Check if we've been having this error for too long
        const lastRefreshAttempt = token.lastRefreshAttempt || 0;
        const now = Date.now();
        const timeSinceLastAttempt = now - lastRefreshAttempt;

        // If it's been more than 30 minutes with refresh errors, invalidate the session
        if (timeSinceLastAttempt > 30 * 60 * 1000) {
          console.error(`Session has had refresh errors for over 30 minutes, invalidating for user: ${token.name || 'unknown'}`);
          return {
            ...session,
            error: "SessionExpired",
            expires: new Date(0).toISOString(),
            // Include minimal user info for logging/debugging
            user: {
              name: token.name,
              email: token.email,
            }
          };
        }

        // Otherwise, continue with the session but include the error
        console.log(`Continuing session despite refresh error for user: ${token.name || 'unknown'}`);
      }

      // Create a minimal session with only essential data
      // This significantly reduces cookie size
      const minimalSession = {
        ...session,
        // Only include the most critical fields
        accessToken: token.accessToken,
        error: token.error,
        email: token.email,
        // Include user object with minimal fields INCLUDING the critical user ID
        user: {
          id: token.sub, // CRITICAL: Include the Keycloak user ID for cart functionality
          name: token.name,
          email: token.email,
        }
      };

      if (isDevelopment || !isProduction) {
        console.log(`[DEV] Created session:`, {
          hasUser: !!minimalSession.user,
          userId: minimalSession.user?.id,
          userName: minimalSession.user?.name,
          userEmail: minimalSession.user?.email,
          hasAccessToken: !!minimalSession.accessToken
        });
      }

      console.log(`✅ SESSION - Including user ID in session: ${token.sub}`);

      // Measure session size
      const sessionSize = JSON.stringify(minimalSession).length;
      console.log(`Session size: ${sessionSize} bytes`);

      return minimalSession;
    },
    async redirect({ url, baseUrl }) {
      try {
        // Handle relative URLs first
        if (url.startsWith('/')) {
          // Relative URL - allowed, prepend baseUrl
          return `${baseUrl}${url}`;
        }

        // Handle absolute URLs - validate the URL format
        try {
          new URL(url);
        } catch (error) {
          // If URL is invalid, return to base URL
          console.warn(`Invalid URL format: ${url}`, error);
          return baseUrl;
        }

        // Special handling for sign-in after logout
        // Force re-authentication when going to login page
        if (url.includes('signin') || url.includes('login')) {
          // Always direct to Keycloak login page for authentication with forced prompt
          return `${KEYCLOAK_ISSUER}/protocol/openid-connect/auth?response_type=code&client_id=${KEYCLOAK_CLIENT_ID}&redirect_uri=${encodeURIComponent(`${baseUrl}/api/auth/callback/keycloak`)}&scope=openid&prompt=login`;
        }

        // Keycloak specific URLs should be allowed
        // Allow redirects to the Keycloak server
        if (url.startsWith(KEYCLOAK_ISSUER!)) {
          return url;
        }

        // Check if the URL is allowed - only redirect to URLs on the same domain
        // or to specifically trusted external domains
        if (url.startsWith(baseUrl)) {
          // Internal URL - allowed
          return url;
        }

        // Keycloak callback handling - extract from URL parameters if present
        const urlObj = new URL(url);
        const callbackUrl = urlObj.searchParams.get('callbackUrl');
        if (callbackUrl) {
          // If there's a callback URL in the parameters, validate and use it
          try {
            const parsedCallbackUrl = new URL(callbackUrl);
            if (parsedCallbackUrl.origin === new URL(baseUrl).origin) {
              return callbackUrl;
            }
          } catch (error) {
            console.warn(`Invalid callback URL: ${callbackUrl}`, error);
          }
        }

        // Check for trusted external domains
        const trustedDomains: string[] = [
          // Add your trusted external domains here
          // The Keycloak server is already handled above
        ];

        if (trustedDomains.some(domain => url.startsWith(domain))) {
          return url;
        }

        // Default: redirect to homepage if URL is not allowed
        console.warn(`Redirect to untrusted URL blocked: ${url}`);
        return baseUrl;
      } catch (error) {
        // In case of any errors (e.g., invalid URL), redirect to base URL
        console.error('Error in redirect callback:', error);
        return baseUrl;
      }
    }
  },
  events: {
    async signOut({ token }) {
      // Perform Keycloak logout
      await doFinalSignoutHandshake(token);

      // Note: We cannot directly manipulate client-side storage here
      // as this runs on the server. Client-side cleanup should be
      // handled in the frontend component that calls signOut()

      console.log("User signed out completely");
    },
    signIn: async ({ user, account }) => {
      // Log successful sign-ins (helpful for audit trails)
      if (user && account) {
        console.log(`User ${user.id} signed in using ${account.provider}`);
      }
    },
    session: async ({ session, token }) => {
      // Track session updates
      if (token.error) {
        console.warn(`Session error for user ${session.user?.name || 'unknown'}: ${token.error}`);
      }
    },
  },
};
