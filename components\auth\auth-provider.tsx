"use client";

import { createContext, useContext, ReactNode } from "react";
import { useAuth, AuthOptions, authHelpers } from "@/hooks/use-auth";
import { AuthModal } from "./auth-modal";

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any;
  session: any;
  status: string;
  requireAuth: (options?: AuthOptions) => boolean;
  closeAuthModal: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * AuthProvider - Provides authentication context and modal to the entire app
 * This should wrap your app to provide consistent auth UX everywhere
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const auth = useAuth();

  return (
    <AuthContext.Provider value={auth}>
      {children}
      
      {/* Global Auth Modal */}
      <AuthModal
        open={auth.showAuthModal}
        onClose={auth.closeAuthModal}
        message={auth.authOptions.message}
        title={auth.authOptions.title}
        description={auth.authOptions.description}
        returnUrl={auth.authOptions.returnUrl}
        showSignUp={auth.authOptions.showSignUp}
        defaultTab={auth.authOptions.defaultTab}
      />
    </AuthContext.Provider>
  );
}

/**
 * Hook to use the auth context
 * This provides the same interface as useAuth but uses the context
 */
export function useAuthContext(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}

/**
 * Higher-order component to protect routes/components
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: AuthOptions
) {
  return function AuthenticatedComponent(props: P) {
    const { requireAuth, isAuthenticated, isLoading } = useAuthContext();

    // Show loading state while checking auth
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      );
    }

    // Check authentication
    if (!isAuthenticated) {
      requireAuth(options);
      return null;
    }

    return <Component {...props} />;
  };
}

/**
 * Component to protect content that requires authentication
 */
interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  options?: AuthOptions;
}

export function AuthGuard({ children, fallback, options }: AuthGuardProps) {
  const { requireAuth, isAuthenticated, isLoading } = useAuthContext();

  // Show loading state while checking auth
  if (isLoading) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )
    );
  }

  // Check authentication
  if (!isAuthenticated) {
    requireAuth(options);
    return fallback || null;
  }

  return <>{children}</>;
}

// Re-export auth helpers for convenience
export { authHelpers };
